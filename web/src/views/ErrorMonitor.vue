<script setup lang="ts">
import type { ColumnDef } from '@tanstack/vue-table'
import type { MonitorEvent } from '@/api/monitor'

import type { FilterField } from '@/components/common/layout'
import { computed, h, onMounted, reactive, ref, watch } from 'vue'
import { toast } from 'vue-sonner'
import { DataTable } from '@/components/common/data-table'
import { DataFilter } from '@/components/common/layout'
import DataCard from '@/components/monitor/DataCard.vue'
import ErrorDetailDialog from '@/components/monitor/ErrorDetailDialog.vue'
import MonitorHeader from '@/components/monitor/MonitorHeader.vue'
import ResponsiveContainer from '@/components/monitor/ResponsiveContainer.vue'
import ResponsiveGrid from '@/components/monitor/ResponsiveGrid.vue'
import { Badge } from '@/components/ui/badge'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { useMonitorStore } from '@/store/monitor'
import { useProjectStore } from '@/store/project'
import {
  formatTimestamp,
  formatUrl,
  getErrorMessage,
  getErrorSeverity,
  getErrorSeverityInfo,
  getErrorTypeName,
} from '@/utils/eventDataMapper'

const projectStore = useProjectStore()
const monitorStore = useMonitorStore()

// 状态
const loading = ref(false)
const showErrorDetail = ref(false)
const selectedError = ref<MonitorEvent | null>(null)
const currentPage = ref(1)

// 筛选条件
const filters = reactive({
  searchQuery: '',
  eventId: '',
  userUuid: '',
  pageUrl: '',
  timeRange: [Date.now() - 24 * 60 * 60 * 1000, Date.now()] as [number, number] | undefined,
})

// 筛选字段配置
const filterFields: FilterField[] = [
  {
    key: 'eventId',
    label: '错误类型',
    type: 'select',
    placeholder: '选择错误类型',
    options: [
      { value: 'script.error', label: '脚本错误' },
      { value: 'resource.error', label: '资源加载错误' },
      { value: 'promise.error', label: 'Promise错误' },
      { value: 'network.error', label: '网络错误' },
      { value: 'custom.error', label: '自定义错误' },
    ],
  },
  {
    key: 'userUuid',
    label: '用户ID',
    type: 'text',
    placeholder: '用户UUID',
  },
  {
    key: 'pageUrl',
    label: '页面URL',
    type: 'text',
    placeholder: '页面地址',
  },
]

// 高级筛选字段配置
const advancedFilterFields: FilterField[] = [
  {
    key: 'timeRange',
    label: '时间范围',
    type: 'date-range',
    placeholder: '选择时间范围',
  },
]

// 计算属性
const currentProject = computed(() => projectStore.currentProject)
const errors = computed(() => monitorStore.errors)
const pagination = computed(() => monitorStore.pagination)

// 统计信息
const errorStats = computed(() => {
  const uniqueUsers = new Set(errors.value.map(e => e.user_uuid)).size
  const uniquePages = new Set(errors.value.map(e => e.trigger_page_url)).size
  const totalErrors = pagination.value.total

  // 按错误类型分组
  const errorsByType = errors.value.reduce((acc, error) => {
    const type = error.event_id
    acc[type] = (acc[type] || 0) + 1
    return acc
  }, {} as Record<string, number>)

  // 按严重程度分组
  const errorsBySeverity = errors.value.reduce((acc, error) => {
    const severity = getErrorSeverity(error.event_data)
    acc[severity] = (acc[severity] || 0) + 1
    return acc
  }, {} as Record<string, number>)

  return {
    totalErrors,
    uniqueUsers,
    uniquePages,
    errorsByType,
    errorsBySeverity,
  }
})

// 表格列配置
const tableColumns = computed<ColumnDef<MonitorEvent>[]>(() => [
  {
    accessorKey: 'event_id',
    header: '错误类型',
    cell: ({ getValue }) => {
      const value = getValue() as string
      const severity = getErrorSeverity({ eventId: value })
      const severityInfo = getErrorSeverityInfo(severity)
      return h('div', { class: 'flex items-center gap-2' }, [
        h(Badge, {
          variant: 'outline',
          class: [severityInfo.color, severityInfo.bgColor],
        }, () => getErrorTypeName(value)),
      ])
    },
  },
  {
    accessorKey: 'event_data',
    header: '错误信息',
    cell: ({ getValue }) => {
      const value = getValue() as any
      const message = getErrorMessage(value)
      return h('div', {
        class: 'max-w-xs truncate',
        title: message,
      }, message)
    },
  },
  {
    accessorKey: 'trigger_page_url',
    header: '页面',
    cell: ({ getValue }) => {
      const value = getValue() as string
      const urlInfo = formatUrl(value, 30)
      return h('div', {
        class: 'max-w-xs truncate',
        title: urlInfo.full,
      }, urlInfo.display)
    },
  },
  {
    accessorKey: 'user_uuid',
    header: '用户',
    cell: ({ getValue }) => {
      const value = getValue() as string
      return h('code', { class: 'text-xs' }, `${value?.slice(0, 8)}...` || '未知')
    },
  },
  {
    accessorKey: 'trigger_time',
    header: '发生时间',
    cell: ({ getValue }) => {
      const value = getValue() as number
      return formatTimestamp(value, 'relative')
    },
  },
  {
    id: 'actions',
    header: '操作',
    cell: ({ row }) => {
      const error = row.original
      return h('button', {
        class: 'text-blue-600 hover:text-blue-800 text-sm',
        onClick: () => handleRowClick(error),
      }, '查看详情')
    },
  },
])

// 时间范围变化处理
function handleTimeRangeChange() {
  currentPage.value = 1
  if (filters.timeRange && filters.timeRange.length === 2) {
    loadData(filters.timeRange[0], filters.timeRange[1])
  }
  else {
    loadData()
  }
}

// 加载数据
async function loadData(startTime?: number, endTime?: number) {
  if (!currentProject.value)
    return

  loading.value = true
  try {
    // 如果没有传入时间参数，使用默认的24小时
    const now = Date.now()
    const defaultStartTime = startTime || (now - 24 * 60 * 60 * 1000)
    const defaultEndTime = endTime || now

    const params: any = {
      project_id: currentProject.value.id,
      page: currentPage.value,
      page_size: 20,
      start_time: defaultStartTime,
      end_time: defaultEndTime,
    }

    if (filters.eventId)
      params.event_id = filters.eventId
    if (filters.userUuid)
      params.user_uuid = filters.userUuid
    if (filters.pageUrl)
      params.page_url = filters.pageUrl

    await monitorStore.fetchErrors(params)
  }
  catch (error) {
    console.error('加载错误数据失败:', error)
    toast.error('加载错误数据失败')
  }
  finally {
    loading.value = false
  }
}

// 刷新数据
function refreshData() {
  currentPage.value = 1
  loadData()
}

// 分页处理
function handlePageChange(page: number) {
  currentPage.value = page
  loadData()
}

// 表格行点击处理
function handleRowClick(record: MonitorEvent) {
  selectedError.value = record
  showErrorDetail.value = true
}

// 筛选条件变化处理
function handleFilterChange() {
  currentPage.value = 1
  handleTimeRangeChange()
}

// 重置筛选条件处理
function handleFilterReset() {
  filters.searchQuery = ''
  filters.eventId = ''
  filters.userUuid = ''
  filters.pageUrl = ''
  filters.timeRange = [Date.now() - 24 * 60 * 60 * 1000, Date.now()]
  currentPage.value = 1
  loadData()
}

// 监听项目切换
watch(() => projectStore.currentProject, (newProject, oldProject) => {
  // 只有当项目真正发生变化时才刷新数据
  if (newProject && oldProject && newProject.id !== oldProject.id) {
    loadData()
  }
}, { deep: true })

// 生命周期
onMounted(() => {
  // 设置默认的时间范围（最近24小时）
  filters.timeRange = [Date.now() - 24 * 60 * 60 * 1000, Date.now()]
})
</script>

<template>
  <ResponsiveContainer type="page" max-width="1400px" centered>
    <!-- 页头 -->
    <MonitorHeader
      title="错误监控"
      description="监控和分析应用程序中的错误和异常"
      :loading="loading"
      :on-refresh="refreshData"
    />

    <!-- 错误统计卡片 -->
    <ResponsiveGrid
      :cols="{ xs: 1, sm: 2, md: 2, lg: 4 }"
      gap="md"
      class="mb-6"
    >
      <DataCard
        title="总错误数"
        description="当前时间范围内的错误总数"
        :value="errorStats.totalErrors"
        status="error"
        :loading="loading"
      />

      <DataCard
        title="影响用户数"
        description="发生错误的唯一用户数"
        :value="errorStats.uniqueUsers"
        status="warning"
        :loading="loading"
      />

      <DataCard
        title="错误页面数"
        description="发生错误的页面数量"
        :value="errorStats.uniquePages"
        status="info"
        :loading="loading"
      />

      <DataCard
        title="严重错误"
        description="高危和严重级别错误"
        :value="(errorStats.errorsBySeverity.critical || 0) + (errorStats.errorsBySeverity.high || 0)"
        status="error"
        :loading="loading"
      />
    </ResponsiveGrid>

    <!-- 错误列表 -->
    <Card>
      <CardHeader>
        <div class="flex justify-between items-center">
          <CardTitle>错误事件列表</CardTitle>
          <div class="flex items-center gap-2">
            <span class="text-sm text-muted-foreground">
              共 {{ pagination.total }} 条记录
            </span>
          </div>
        </div>
      </CardHeader>
      <CardContent class="space-y-4">
        <!-- 筛选条件 -->
        <DataFilter
          v-model="filters"
          :total-items="pagination.total"
          search-placeholder="搜索错误信息、页面URL..."
          :quick-filters="filterFields"
          :advanced-filters="advancedFilterFields"
          @update:model-value="handleFilterChange"
          @reset="handleFilterReset"
        />

        <!-- 数据表格 -->
        <DataTable
          :data="errors"
          :columns="tableColumns"
          :loading="loading"
          :total="pagination.total"
          :page-size="20"
          :current-page="currentPage"
          empty-title="暂无错误事件"
          empty-description="没有找到相关错误数据"
          @page-change="handlePageChange"
        />
      </CardContent>
    </Card>

    <!-- 错误详情对话框 -->
    <ErrorDetailDialog
      v-model:open="showErrorDetail"
      :error="selectedError"
    />
  </ResponsiveContainer>
</template>

<style scoped>
.error-monitor {
  padding: 1.5rem;
}

@media (min-width: 768px) {
  .error-monitor {
    padding: 2rem;
  }
}
</style>
