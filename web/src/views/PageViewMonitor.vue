<script setup lang="ts">
import type { ColumnDef } from '@tanstack/vue-table'
import type { MonitorEvent } from '@/api/monitor'

import type { FilterField } from '@/components/common/layout'
import { Eye } from 'lucide-vue-next'
import { computed, h, onMounted, reactive, ref, watch } from 'vue'
import { toast } from 'vue-sonner'
import { DataTable } from '@/components/common/data-table'
import { DataFilter } from '@/components/common/layout'
import DataCard from '@/components/monitor/DataCard.vue'
import MonitorHeader from '@/components/monitor/MonitorHeader.vue'
import PageViewDetailDialog from '@/components/monitor/PageViewDetailDialog.vue'
import ResponsiveContainer from '@/components/monitor/ResponsiveContainer.vue'
import ResponsiveGrid from '@/components/monitor/ResponsiveGrid.vue'
import { Badge } from '@/components/ui/badge'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { useMonitorStore } from '@/store/monitor'
import { useProjectStore } from '@/store/project'
import {
  formatDuration,
  formatTimestamp,
  formatUrl,
  getDeviceInfo,
  getPageTitle,
} from '@/utils/eventDataMapper'

const projectStore = useProjectStore()
const monitorStore = useMonitorStore()

// 状态
const loading = ref(false)
const showVisitDetail = ref(false)
const selectedVisit = ref<MonitorEvent | null>(null)
const currentPage = ref(1)

// 筛选条件
const filters = reactive({
  searchQuery: '',
  pageUrl: '',
  userUuid: '',
  visitType: '',
  timeRange: [Date.now() - 24 * 60 * 60 * 1000, Date.now()] as [number, number] | undefined,
})

// 筛选字段配置
const filterFields: FilterField[] = [
  {
    key: 'pageUrl',
    label: '页面URL',
    type: 'text',
    placeholder: '页面地址',
  },
  {
    key: 'userUuid',
    label: '用户ID',
    type: 'text',
    placeholder: '用户UUID',
  },
  {
    key: 'visitType',
    label: '访问类型',
    type: 'select',
    placeholder: '选择访问类型',
    options: [
      { value: 'first', label: '首次访问' },
      { value: 'return', label: '回访' },
      { value: 'direct', label: '直接访问' },
      { value: 'referral', label: '外链访问' },
    ],
  },
]

// 高级筛选字段配置
const advancedFilterFields: FilterField[] = [
  {
    key: 'timeRange',
    label: '时间范围',
    type: 'date-range',
    placeholder: '选择时间范围',
  },
]

// 计算属性
const currentProject = computed(() => projectStore.currentProject)
const pageViews = computed(() => monitorStore.pageViews)
const pagination = computed(() => monitorStore.pagination)

// 页面访问统计
const pageViewStats = computed(() => {
  const data = pageViews.value

  // 基础统计
  const totalViews = pagination.value.total
  const uniqueVisitors = new Set(data.map(v => v.user_uuid)).size
  const uniquePages = new Set(data.map(v => v.trigger_page_url)).size

  // 计算平均停留时长（如果有duration数据）
  const durationsWithData = data.filter(v => v.event_data?.duration).map(v => v.event_data.duration)
  const averageDuration = durationsWithData.length > 0
    ? Math.round(durationsWithData.reduce((a, b) => a + b, 0) / durationsWithData.length)
    : 0

  // 热门页面统计
  const pageStats = data.reduce((acc, visit) => {
    const url = visit.trigger_page_url
    const title = getPageTitle(visit.event_data) || formatUrl(url).display

    if (!acc[url]) {
      acc[url] = { url, title, count: 0 }
    }
    acc[url].count++
    return acc
  }, {} as Record<string, { url: string, title: string, count: number }>)

  const popularPages = Object.values(pageStats)
    .sort((a, b) => b.count - a.count)
    .slice(0, 5)

  // 设备类型统计
  const deviceStats = data.reduce((acc, visit) => {
    const deviceInfo = getDeviceInfo(visit.event_data)
    acc[deviceInfo.deviceType] = (acc[deviceInfo.deviceType] || 0) + 1
    return acc
  }, {} as Record<string, number>)

  return {
    totalViews,
    uniqueVisitors,
    uniquePages,
    averageDuration,
    popularPages,
    deviceStats,
  }
})

// 表格列配置
const tableColumns = computed<ColumnDef<MonitorEvent>[]>(() => [
  {
    accessorKey: 'trigger_page_url',
    header: '页面',
    cell: ({ getValue, row }) => {
      const value = getValue() as string
      const record = row.original
      const urlInfo = formatUrl(value, 50)
      const title = getPageTitle(record.event_data)
      return h('div', { class: 'space-y-1' }, [
        h('div', {
          class: 'font-medium truncate',
          title: title || urlInfo.full,
        }, title || urlInfo.display),
        h('div', {
          class: 'text-xs text-muted-foreground truncate',
          title: urlInfo.full,
        }, urlInfo.domain),
      ])
    },
  },
  {
    accessorKey: 'event_data',
    header: '访问信息',
    cell: ({ getValue }) => {
      const value = getValue() as any
      const deviceInfo = getDeviceInfo(value)
      const duration = value?.duration
      return h('div', { class: 'text-xs space-y-1' }, [
        h('div', `${deviceInfo.platform} / ${deviceInfo.browser}`),
        h('div', `${deviceInfo.deviceType} (${deviceInfo.screenSize})`),
        duration ? h('div', `停留: ${formatDuration(duration)}`) : null,
      ].filter(Boolean))
    },
  },
  {
    accessorKey: 'user_uuid',
    header: '用户',
    cell: ({ getValue }) => {
      const value = getValue() as string
      return h('code', { class: 'text-xs' }, `${value?.slice(0, 8)}...` || '未知')
    },
  },
  {
    accessorKey: 'trigger_time',
    header: '访问时间',
    cell: ({ getValue }) => {
      const value = getValue() as number
      return formatTimestamp(value, 'relative')
    },
  },
  {
    id: 'actions',
    header: '操作',
    cell: ({ row }) => {
      const visit = row.original
      return h('button', {
        class: 'text-blue-600 hover:text-blue-800 text-sm',
        onClick: () => handleRowClick(visit),
      }, '查看详情')
    },
  },
])

// 表格行点击处理
function handleRowClick(record: MonitorEvent) {
  selectedVisit.value = record
  showVisitDetail.value = true
}

// 时间范围变化处理
function handleTimeRangeChange() {
  currentPage.value = 1
  if (filters.timeRange && filters.timeRange.length === 2) {
    loadData(filters.timeRange[0], filters.timeRange[1])
  } else {
    loadData()
  }
}

// 加载数据
async function loadData(startTime?: number, endTime?: number) {
  if (!currentProject.value)
    return

  loading.value = true
  try {
    // 如果没有传入时间参数，使用默认的24小时
    const now = Date.now()
    const defaultStartTime = startTime || (now - 24 * 60 * 60 * 1000)
    const defaultEndTime = endTime || now

    const params: any = {
      project_id: currentProject.value.id,
      page: currentPage.value,
      page_size: 20,
      start_time: defaultStartTime,
      end_time: defaultEndTime,
    }

    if (filters.pageUrl)
      params.page_url = filters.pageUrl
    if (filters.userUuid)
      params.user_uuid = filters.userUuid
    if (filters.visitType)
      params.visit_type = filters.visitType

    await monitorStore.fetchPageViews(params)
  }
  catch (error) {
    console.error('加载页面访问数据失败:', error)
    toast.error('加载页面访问数据失败')
  }
  finally {
    loading.value = false
  }
}

// 刷新数据
function refreshData() {
  currentPage.value = 1
  loadData()
}

// 筛选条件变化处理
function handleFilterChange() {
  currentPage.value = 1
  handleTimeRangeChange()
}

// 重置筛选条件处理
function handleFilterReset() {
  filters.searchQuery = ''
  filters.pageUrl = ''
  filters.userUuid = ''
  filters.visitType = ''
  filters.timeRange = [Date.now() - 24 * 60 * 60 * 1000, Date.now()]
  currentPage.value = 1
  loadData()
}

// 分页处理
function handlePageChange(page: number) {
  currentPage.value = page
  loadData()
}

// 监听项目切换
watch(() => projectStore.currentProject, (newProject, oldProject) => {
  // 只有当项目真正发生变化时才刷新数据
  if (newProject && oldProject && newProject.id !== oldProject.id) {
    loadData()
  }
}, { deep: true })

// 生命周期
onMounted(() => {
  // 设置默认的时间范围（最近24小时）
  filters.timeRange = [Date.now() - 24 * 60 * 60 * 1000, Date.now()]
})
</script>

<template>
  <ResponsiveContainer type="page" max-width="1400px" centered>
    <!-- 页头 -->
    <MonitorHeader
      title="页面访问监控"
      description="监控和分析页面访问情况"
      :loading="loading"
      :on-refresh="refreshData"
    />

    <!-- 统计卡片 -->
    <ResponsiveGrid
      :cols="{ xs: 1, sm: 2, md: 2, lg: 4 }"
      gap="md"
      class="mb-6"
    >
      <DataCard
        title="总访问量"
        description="页面访问总数"
        :value="pageViewStats.totalViews"
        status="info"
        :loading="loading"
      />

      <DataCard
        title="独立访客"
        description="唯一访问用户数"
        :value="pageViewStats.uniqueVisitors"
        status="success"
        :loading="loading"
      />

      <DataCard
        title="访问页面数"
        description="被访问的页面数量"
        :value="pageViewStats.uniquePages"
        status="warning"
        :loading="loading"
      />

      <DataCard
        title="平均停留时长"
        description="用户平均停留时间"
        :value="pageViewStats.averageDuration > 0 ? formatDuration(pageViewStats.averageDuration) : '暂无数据'"
        status="info"
        :loading="loading"
      />
    </ResponsiveGrid>

    <!-- 热门页面排行 -->
    <ResponsiveGrid
      :cols="{ xs: 1, lg: 2 }"
      gap="lg"
      class="mb-6"
    >
      <Card>
        <CardHeader>
          <CardTitle class="text-lg">
            热门页面
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div v-if="pageViewStats.popularPages.length > 0" class="space-y-3">
            <div
              v-for="(page, index) in pageViewStats.popularPages"
              :key="page.url"
              class="flex items-center justify-between p-3 bg-muted rounded-lg"
            >
              <div class="flex-1">
                <div class="flex items-center gap-2">
                  <Badge variant="outline" class="text-xs">
                    {{ index + 1 }}
                  </Badge>
                  <span class="text-sm font-medium truncate">{{ page.title }}</span>
                </div>
                <p class="text-xs text-muted-foreground mt-1 truncate">
                  {{ page.url }}
                </p>
              </div>
              <div class="text-right">
                <div class="text-lg font-bold">
                  {{ page.count }}
                </div>
                <div class="text-xs text-muted-foreground">
                  访问次数
                </div>
              </div>
            </div>
          </div>
          <div v-else class="text-center py-8 text-muted-foreground">
            <Eye class="w-12 h-12 mx-auto mb-2" />
            <p>暂无热门页面数据</p>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle class="text-lg">
            设备类型分布
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div v-if="Object.keys(pageViewStats.deviceStats).length > 0" class="space-y-3">
            <div
              v-for="(count, deviceType) in pageViewStats.deviceStats"
              :key="deviceType"
              class="flex items-center justify-between p-3 bg-muted rounded-lg"
            >
              <div class="flex items-center gap-2">
                <span class="text-sm font-medium capitalize">{{ deviceType }}</span>
              </div>
              <div class="text-right">
                <div class="text-lg font-bold">
                  {{ count }}
                </div>
                <div class="text-xs text-muted-foreground">
                  {{ ((count / pageViewStats.totalViews) * 100).toFixed(1) }}%
                </div>
              </div>
            </div>
          </div>
          <div v-else class="text-center py-8 text-muted-foreground">
            <Eye class="w-12 h-12 mx-auto mb-2" />
            <p>暂无设备数据</p>
          </div>
        </CardContent>
      </Card>
    </ResponsiveGrid>

    <!-- 访问记录列表 -->
    <Card>
      <CardHeader>
        <div class="flex justify-between items-center">
          <CardTitle>访问记录</CardTitle>
          <div class="flex items-center gap-2">
            <span class="text-sm text-muted-foreground">
              共 {{ pagination.total }} 条记录
            </span>
          </div>
        </div>
      </CardHeader>
      <CardContent class="space-y-4">
        <!-- 筛选条件 -->
        <DataFilter
          v-model="filters"
          :total-items="pagination.total"
          search-placeholder="搜索页面标题、URL..."
          :quick-filters="filterFields"
          :advanced-filters="advancedFilterFields"
          @update:model-value="handleFilterChange"
          @reset="handleFilterReset"
        />

        <!-- 数据表格 -->
        <DataTable
          :data="pageViews"
          :columns="tableColumns"
          :loading="loading"
          :total="pagination.total"
          :page-size="20"
          :current-page="currentPage"
          empty-title="暂无页面访问记录"
          empty-description="没有找到相关访问数据"
          @page-change="handlePageChange"
        />
      </CardContent>
    </Card>

    <!-- 访问详情对话框 -->
    <PageViewDetailDialog
      v-model:open="showVisitDetail"
      :event="selectedVisit"
    />
  </ResponsiveContainer>
</template>

<style scoped>
.page-view-monitor {
  padding: 1.5rem;
}

@media (min-width: 768px) {
  .page-view-monitor {
    padding: 2rem;
  }
}
</style>
