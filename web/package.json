{"name": "web", "type": "module", "version": "0.0.0", "private": true, "packageManager": "pnpm@9.1.4+sha1.2432063d815cfa88fd9fef1d85a445e3f609851d", "scripts": {"dev": "vite", "build": "vue-tsc -b && vite build", "preview": "vite preview"}, "dependencies": {"@internationalized/date": "^3.8.2", "@tanstack/vue-table": "^8.21.3", "@vee-validate/zod": "^4.15.1", "@vueuse/core": "^13.5.0", "axios": "^1.10.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lucide-vue-next": "^0.525.0", "pinia": "^3.0.3", "pinia-plugin-persistedstate": "^4.4.1", "reka-ui": "^2.3.2", "tailwind-merge": "^3.3.1", "tw-animate-css": "^1.3.4", "vaul-vue": "^0.4.1", "vee-validate": "^4.15.1", "vue": "^3.5.17", "vue-router": "^4.5.1", "vue-sonner": "^2.0.1", "zod": "^3.25.71"}, "devDependencies": {"@antfu/eslint-config": "^4.16.2", "@tailwindcss/vite": "^4.1.11", "@types/node": "^24.0.10", "@vitejs/plugin-vue": "^6.0.0", "@vue/tsconfig": "^0.7.0", "@web-tracing/vue3": "^2.0.9", "eslint": "^9.30.1", "tailwindcss": "^4.1.11", "typescript": "~5.8.3", "vite": "^7.0.0", "vue-tsc": "^2.2.10"}}